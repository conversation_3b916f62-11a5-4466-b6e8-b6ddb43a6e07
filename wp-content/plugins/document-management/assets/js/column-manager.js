/**
 * 栏目管理前端交互脚本
 */
(function($) {
    'use strict';
    
    var ColumnManager = {
        
        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 筛选按钮
            $('#filter-posts').on('click', function() {
                self.filterPosts();
            });
            
            // 删除按钮
            $(document).on('click', '.delete-column', function() {
                var postId = $(this).data('id');
                self.deleteColumn(postId, $(this).closest('.column-item'));
            });

            // 添加子栏目按钮
            $(document).on('click', '.add-sub-column', function() {
                var parentId = $(this).data('parent-id');
                self.showAddSubColumnDialog(parentId, $(this).closest('.column-item'));
            });

            // 修改排序按钮
            $(document).on('click', '.edit-order-btn', function() {
                var postId = $(this).data('post-id');
                var currentOrder = $(this).data('current-order');
                var postTitle = $(this).data('post-title');
                self.showEditOrderDialog(postId, currentOrder, postTitle);
            });

            // 状态筛选下拉框
            $('#post-status-filter').on('change', function() {
                self.filterPosts();
            });

            // 搜索输入框
            $('#search-posts').on('input', function() {
                clearTimeout(self.searchTimeout);
                self.searchTimeout = setTimeout(function() {
                    self.filterPosts();
                }, 500); // 延迟500ms搜索，避免频繁请求
            });

            // 清除筛选按钮
            $('#clear-filters').on('click', function() {
                $('#search-posts').val('');
                $('#post-status-filter').val('');
                self.filterPosts();
            });
        },
        
        /**
         * 更新单个栏目排序
         */
        updateSingleOrder: function(postId, menuOrder) {
            var self = this;

            // 验证输入
            if (!postId || menuOrder === '') {
                this.showMessage('参数错误', 'error');
                return;
            }

            var order = parseInt(menuOrder);
            if (isNaN(order) || order < 0 || order > 999) {
                this.showMessage('排序值必须在0-999之间', 'error');
                return;
            }

            this.showSaving();

            $.ajax({
                url: columnManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'update_single_order',
                    nonce: columnManager.nonce,
                    post_id: postId,
                    menu_order: order
                },
                success: function(response) {
                    if (response.success) {
                        self.showMessage(response.data.message, 'success');
                        // 刷新页面显示最新排序
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        self.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    self.showMessage(columnManager.strings.error, 'error');
                },
                complete: function() {
                    self.hideSaving();
                }
            });
        },
        

        
        /**
         * 删除栏目
         */
        deleteColumn: function(postId, element) {
            var self = this;
            var postTitle = element.find('.column-item-title strong').first().text();

            if (!confirm('确定要删除文档 "' + postTitle + '" 吗？\n\n此操作不可撤销。')) {
                return;
            }
            
            // 检查是否有子栏目
            if (element.find('.column-sublist .column-item').length > 0) {
                this.showMessage('请先删除子栏目', 'error');
                return;
            }
            
            $.ajax({
                url: columnManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'delete_column',
                    nonce: columnManager.nonce,
                    post_id: postId
                },
                success: function(response) {
                    if (response.success) {
                        element.fadeOut(300, function() {
                            $(this).remove();
                        });
                        self.showMessage(response.data.message, 'success');
                    } else {
                        self.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    self.showMessage(columnManager.strings.error, 'error');
                }
            });
        },
        
        /**
         * 筛选文章
         */
        filterPosts: function() {
            var self = this;
            var status = $('#post-status-filter').val();
            var search = $('#search-posts').val();

            this.showSaving();

            $.ajax({
                url: columnManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'filter_columns',
                    nonce: columnManager.nonce,
                    status: status,
                    search: search
                },
                success: function(response) {
                    if (response.success) {
                        $('#column-list-container').html(response.data.html);
                        self.showMessage(response.data.message, 'success');
                    } else {
                        self.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    self.showMessage(columnManager.strings.error, 'error');
                },
                complete: function() {
                    self.hideSaving();
                }
            });
        },
        
        /**
         * 显示保存状态
         */
        showSaving: function() {
            $('.spinner').addClass('is-active');
            $('#save-status').text(columnManager.strings.saving);
        },
        
        /**
         * 隐藏保存状态
         */
        hideSaving: function() {
            $('.spinner').removeClass('is-active');
            $('#save-status').text('');
        },
        
        /**
         * 显示修改排序对话框
         */
        showEditOrderDialog: function(postId, currentOrder, postTitle) {
            var self = this;

            var dialog = $('<div class="edit-order-dialog">' +
                '<div class="dialog-overlay"></div>' +
                '<div class="dialog-content">' +
                    '<h3>修改排序</h3>' +
                    '<p>文档: <strong>' + postTitle + '</strong></p>' +
                    '<label for="new-order">新排序值:</label>' +
                    '<input type="number" id="new-order" class="regular-text" value="' + currentOrder + '" min="0" max="999" step="1">' +
                    '<div class="dialog-buttons">' +
                        '<button type="button" class="button button-primary" id="save-order">保存</button>' +
                        '<button type="button" class="button" id="cancel-order">取消</button>' +
                    '</div>' +
                '</div>' +
            '</div>');

            $('body').append(dialog);

            // 聚焦到输入框并选中内容
            $('#new-order').focus().select();

            // 绑定事件
            $('#save-order').on('click', function() {
                var newOrder = $('#new-order').val().trim();
                if (newOrder !== '' && !isNaN(newOrder)) {
                    self.updateSingleOrder(postId, parseInt(newOrder));
                    dialog.remove();
                } else {
                    alert('请输入有效的排序值');
                }
            });

            $('#cancel-order, .dialog-overlay').on('click', function() {
                dialog.remove();
            });

            // 回车键保存
            $('#new-order').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#save-order').click();
                }
            });
        },

        /**
         * 显示添加子栏目对话框
         */
        showAddSubColumnDialog: function(parentId, parentElement) {
            var self = this;
            var parentTitle = parentElement.find('.column-item-title strong').first().text();

            var dialog = $('<div class="add-sub-column-dialog">' +
                '<div class="dialog-overlay"></div>' +
                '<div class="dialog-content">' +
                    '<h3>添加子栏目</h3>' +
                    '<p>父栏目: <strong>' + parentTitle + '</strong></p>' +
                    '<label for="sub-column-title">子栏目标题:</label>' +
                    '<input type="text" id="sub-column-title" class="regular-text" placeholder="请输入子栏目标题">' +
                    '<div class="dialog-buttons">' +
                        '<button type="button" class="button button-primary" id="create-sub-column">创建</button>' +
                        '<button type="button" class="button" id="cancel-sub-column">取消</button>' +
                    '</div>' +
                '</div>' +
            '</div>');

            $('body').append(dialog);

            // 聚焦到输入框
            $('#sub-column-title').focus();

            // 绑定事件
            $('#create-sub-column').on('click', function() {
                var title = $('#sub-column-title').val().trim();
                if (title) {
                    self.createSubColumn(parentId, title, parentElement);
                    dialog.remove();
                } else {
                    alert('请输入子栏目标题');
                }
            });

            $('#cancel-sub-column, .dialog-overlay').on('click', function() {
                dialog.remove();
            });

            // 回车键创建
            $('#sub-column-title').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#create-sub-column').click();
                }
            });
        },

        /**
         * 创建子栏目
         */
        createSubColumn: function(parentId, title, parentElement) {
            var self = this;

            this.showSaving();

            $.ajax({
                url: columnManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'create_sub_column',
                    nonce: columnManager.nonce,
                    parent_id: parentId,
                    title: title
                },
                success: function(response) {
                    if (response.success) {
                        self.showMessage(response.data.message, 'success');
                        // 刷新页面显示
                        self.filterPosts();
                    } else {
                        self.showMessage(response.data.message, 'error');
                    }
                },
                error: function() {
                    self.showMessage(columnManager.strings.error, 'error');
                },
                complete: function() {
                    self.hideSaving();
                }
            });
        },

        /**
         * 显示消息
         */
        showMessage: function(message, type) {
            var className = type === 'success' ? 'notice-success' : 'notice-error';
            var notice = $('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');

            // 移除现有通知
            $('.notice').remove();

            // 添加新通知
            $('.wrap h1').after(notice);

            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(function() {
                    notice.fadeOut();
                }, 3000);
            }

            // 添加关闭按钮功能
            notice.on('click', '.notice-dismiss', function() {
                notice.fadeOut();
            });
        }
    };
    
    // 文档就绪时初始化
    $(document).ready(function() {
        if ($('#column-manager-container').length) {
            ColumnManager.init();
        }
    });
    
})(jQuery);
