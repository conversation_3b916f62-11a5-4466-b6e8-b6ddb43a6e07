/**
 * 文档菜单管理样式文件
 */

/* 主容器 */
#column-manager-container {
    margin-top: 20px;
}

/* 工具栏 */
.column-manager-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: none;
    padding: 15px 20px;
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.column-manager-toolbar .alignleft {
    float: left;
}

.column-manager-toolbar .alignright {
    float: right;
}

.column-manager-toolbar .clear {
    clear: both;
}

.search-input {
    margin-right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 5px 12px;
    width: 200px;
    color: #333;
}

.search-input::placeholder {
    color: rgba(51, 51, 51, 0.6);
}

.column-manager-toolbar select {
    margin-right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 5px 10px;
}

.column-manager-toolbar .button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.column-manager-toolbar .button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

#save-status {
    margin-left: 10px;
    font-style: italic;
    color: rgba(255, 255, 255, 0.8);
}

/* 文档列表容器 */
#column-list-container {
    background: #fff;
    border: none;
    border-radius: 12px;
    min-height: 300px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

/* 文档列表 */
.column-list {
    margin: 0;
    padding: 20px;
    list-style: none;
    min-height: 50px;
}

.column-sublist {
    margin: 10px 0 0 0;
    padding: 15px;
    list-style: none;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-radius: 8px;
    border-left: 4px solid #667eea;
    margin-left: 20px;
}

/* 文档项 */
.column-item {
    margin: 0 0 15px 0;
    padding: 0;
    position: relative;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.column-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.column-item:last-child {
    margin-bottom: 0;
}

/* 文档项句柄 */
.column-item-handle {
    padding: 20px;
    background: transparent;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border-radius: 10px;
}

/* 复选框区域 */
.column-item-checkbox {
    flex-shrink: 0;
}

.post-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.column-item-handle:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

/* 文档项信息区域 */
.column-item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 控制按钮区域调整 */
.column-item-handle {
    justify-content: flex-start;
}

.column-item-controls {
    margin-left: auto;
}

/* 文档标题 */
.column-item-title {
    flex: 1;
    font-size: 16px;
    line-height: 1.5;
}

.column-item-title strong {
    color: #2d3748;
    font-weight: 700;
    font-size: 16px;
}

.post-status {
    color: #718096;
    font-weight: 500;
    margin-left: 10px;
    font-size: 13px;
    padding: 2px 8px;
    background: rgba(113, 128, 150, 0.1);
    border-radius: 12px;
}

.order-display {
    color: #667eea;
    font-weight: 600;
    margin-left: 10px;
    font-size: 13px;
    padding: 3px 10px;
    background: linear-gradient(135deg, #e8f0ff, #f0f4ff);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
}

.children-count {
    color: #667eea;
    font-weight: 600;
    margin-left: 10px;
    font-size: 12px;
    padding: 3px 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border-radius: 12px;
}

/* 状态样式 */
.status-publish .post-status {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: #fff;
}

.status-draft .post-status {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: #fff;
}

.status-private .post-status {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: #fff;
}

/* 控制按钮 */
.column-item-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.column-item-controls .button {
    font-size: 13px;
    height: auto;
    padding: 8px 16px;
    line-height: 1.4;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.column-item-controls .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 层级样式 */
.depth-0 {
    background: #fff;
}

.depth-1 .column-item {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-left: 4px solid #667eea;
}

.depth-1 .column-item-handle {
    background: transparent;
}

.depth-1:hover .column-item-handle {
    background: rgba(102, 126, 234, 0.05);
}

.depth-1 .column-item-title strong {
    color: #667eea;
    font-size: 15px;
}

.depth-1 .column-item-title strong:before {
    content: "└ ";
    color: #a0aec0;
    font-weight: normal;
    margin-right: 5px;
}

/* 更新按钮样式 */
.update-order {
    font-size: 12px;
    padding: 6px 12px;
    height: auto;
    line-height: 1.2;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.update-order:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 空状态 */
.no-items {
    text-align: center;
    padding: 80px 40px;
    color: #646970;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-radius: 12px;
    margin: 20px;
}

.no-items-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.no-items h3 {
    color: #2d3748;
    font-size: 24px;
    margin: 0 0 15px 0;
    font-weight: 600;
}

.no-items p {
    color: #718096;
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 25px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.no-items .button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: #fff;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.no-items .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 加载状态 */
.spinner.is-active {
    float: none;
    margin: 0;
    visibility: visible;
}

/* 通知样式调整 */
.notice {
    margin: 15px 0 5px;
}

/* 响应式设计 */
@media screen and (max-width: 782px) {
    .column-item-handle {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .column-item-info {
        width: 100%;
    }

    .column-order-control {
        flex-wrap: wrap;
    }

    .column-item-controls {
        width: 100%;
        justify-content: flex-end;
    }

    .column-manager-toolbar .alignleft,
    .column-manager-toolbar .alignright {
        float: none;
        margin-bottom: 10px;
    }

    .column-sublist {
        margin-left: 15px;
        padding-left: 10px;
    }
}

@media screen and (max-width: 480px) {
    .column-item-handle {
        padding: 10px 15px;
    }
    
    .column-item-controls {
        flex-direction: column;
        gap: 5px;
    }
    
    .column-item-controls .button {
        width: 100%;
        text-align: center;
    }
}

/* 辅助功能 */
.column-item-handle:focus {
    outline: 2px solid #2271b1;
    outline-offset: -2px;
}

/* 动画效果 */
.column-item {
    transition: all 0.2s ease;
}

.column-item:hover {
    transform: translateX(2px);
}

/* 删除确认动画 */
.column-item.deleting {
    opacity: 0.5;
    transform: scale(0.95);
}

/* 对话框通用样式 */
.add-sub-column-dialog,
.edit-order-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
}

.dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.dialog-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 20px;
    border-radius: 3px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
    min-width: 400px;
    max-width: 90%;
}

.dialog-content h3 {
    margin: 0 0 15px;
    font-size: 16px;
    color: #1d2327;
}

.dialog-content label {
    display: block;
    margin: 10px 0 5px;
    font-weight: 600;
}

.dialog-content input[type="text"] {
    width: 100%;
    margin-bottom: 15px;
}

.dialog-buttons {
    text-align: right;
    margin-top: 20px;
}

.dialog-buttons .button {
    margin-left: 10px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .column-item-handle {
        border: 1px solid #000;
    }

    .sortable-placeholder {
        border-color: #000;
        background: #fff;
    }

    .depth-1 .column-item-handle {
        border-left-color: #000;
    }
}
