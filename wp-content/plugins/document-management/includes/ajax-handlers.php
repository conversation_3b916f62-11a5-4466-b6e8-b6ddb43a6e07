<?php
/**
 * AJAX处理函数
 *
 * @package Document_Management
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 注册AJAX处理函数
 */
add_action('wp_ajax_update_column_order', 'ajax_update_column_order');
add_action('wp_ajax_update_column_parent', 'ajax_update_column_parent');
add_action('wp_ajax_delete_column', 'ajax_delete_column');
add_action('wp_ajax_filter_columns', 'ajax_filter_columns');
add_action('wp_ajax_create_sub_column', 'ajax_create_sub_column');
add_action('wp_ajax_update_single_order', 'ajax_update_single_order');

// 前端AJAX处理
add_action('wp_ajax_get_docs_menu', 'ajax_get_docs_menu');
add_action('wp_ajax_nopriv_get_docs_menu', 'ajax_get_docs_menu');
add_action('wp_ajax_get_doc_content', 'ajax_get_doc_content');
add_action('wp_ajax_nopriv_get_doc_content', 'ajax_get_doc_content');

// 设置页面AJAX处理
add_action('wp_ajax_flush_rewrite_rules', 'ajax_flush_rewrite_rules');

/**
 * 处理栏目排序更新
 */
function ajax_update_column_order() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }
    
    // 检查用户权限
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }
    
    // 获取排序数据
    $order_data = isset($_POST['order']) ? $_POST['order'] : array();
    
    if (empty($order_data)) {
        wp_send_json_error(array('message' => __('排序数据为空', 'document-management')));
    }
    
    // 更新排序
    $success_count = 0;
    foreach ($order_data as $item) {
        $post_id = intval($item['id']);
        $menu_order = intval($item['order']);
        $parent_id = intval($item['parent']);
        
        // 验证文章存在
        if (!get_post($post_id)) {
            continue;
        }
        
        // 限制层级深度（最多2级）
        if ($parent_id > 0) {
            $parent_post = get_post($parent_id);
            if ($parent_post && $parent_post->post_parent > 0) {
                // 如果父级已经是子级，则不允许再添加子级
                continue;
            }
        }
        
        // 更新文章
        $result = wp_update_post(array(
            'ID' => $post_id,
            'menu_order' => $menu_order,
            'post_parent' => $parent_id
        ));
        
        if ($result) {
            $success_count++;
            
            // 更新深度元数据
            $depth = $parent_id > 0 ? 1 : 0;
            if ($parent_id > 0) {
                $grandparent = get_post($parent_id);
                if ($grandparent && $grandparent->post_parent > 0) {
                    $depth = 2;
                }
            }
            update_post_meta($post_id, '_column_depth', $depth);

            // 清理相关缓存
            wp_cache_delete('column_manager_posts_' . md5(serialize(array())), 'document_management');
        }
    }
    
    if ($success_count > 0) {
        wp_send_json_success(array(
            'message' => sprintf(__('成功更新 %d 个栏目', 'document-management'), $success_count)
        ));
    } else {
        wp_send_json_error(array('message' => __('更新失败', 'document-management')));
    }
}

/**
 * 处理栏目父级更新
 */
function ajax_update_column_parent() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }
    
    // 检查用户权限
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }
    
    $post_id = intval($_POST['post_id']);
    $parent_id = intval($_POST['parent_id']);
    
    // 验证文章存在
    if (!get_post($post_id)) {
        wp_send_json_error(array('message' => __('栏目不存在', 'document-management')));
    }
    
    // 防止循环引用
    if ($post_id === $parent_id) {
        wp_send_json_error(array('message' => __('不能将栏目设为自己的父级', 'document-management')));
    }
    
    // 检查层级深度
    if ($parent_id > 0) {
        $parent_post = get_post($parent_id);
        if ($parent_post && $parent_post->post_parent > 0) {
            wp_send_json_error(array('message' => __('最多只支持2级层级', 'document-management')));
        }
        
        // 检查是否会造成循环引用
        if (is_post_ancestor($post_id, $parent_id)) {
            wp_send_json_error(array('message' => __('不能将栏目移动到其子级下', 'document-management')));
        }
    }
    
    // 更新父级
    $result = wp_update_post(array(
        'ID' => $post_id,
        'post_parent' => $parent_id
    ));
    
    if ($result) {
        // 更新深度元数据
        $depth = $parent_id > 0 ? 1 : 0;
        update_post_meta($post_id, '_column_depth', $depth);
        
        wp_send_json_success(array('message' => __('父级更新成功', 'document-management')));
    } else {
        wp_send_json_error(array('message' => __('父级更新失败', 'document-management')));
    }
}

/**
 * 处理栏目删除
 */
function ajax_delete_column() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }
    
    // 检查用户权限
    if (!current_user_can('delete_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }
    
    $post_id = intval($_POST['post_id']);
    
    // 验证文章存在
    $post = get_post($post_id);
    if (!$post) {
        wp_send_json_error(array('message' => __('栏目不存在', 'document-management')));
    }
    
    // 检查是否有子栏目
    $children = get_children(array(
        'post_parent' => $post_id,
        'post_type' => 'post',
        'numberposts' => 1
    ));
    
    if (!empty($children)) {
        wp_send_json_error(array('message' => __('请先删除子栏目', 'document-management')));
    }
    
    // 删除文章
    $result = wp_delete_post($post_id, true);
    
    if ($result) {
        wp_send_json_success(array('message' => __('栏目删除成功', 'document-management')));
    } else {
        wp_send_json_error(array('message' => __('栏目删除失败', 'document-management')));
    }
}

/**
 * 处理栏目筛选
 */
function ajax_filter_columns() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }
    
    // 检查用户权限
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }
    
    $status = sanitize_text_field($_POST['status']);

    // 获取栏目管理器实例
    $column_manager = Column_Manager::get_instance();
    $hierarchical_posts = $column_manager->get_hierarchical_posts($status);

    // 开始输出缓冲
    ob_start();

    // 渲染栏目列表
    $column_manager->render_column_list($hierarchical_posts);

    $html = ob_get_clean();
    
    wp_send_json_success(array(
        'html' => $html,
        'message' => __('筛选完成', 'document-management')
    ));
}

/**
 * 处理单个栏目排序更新
 */
function ajax_update_single_order() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }

    // 检查用户权限
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }

    $post_id = intval($_POST['post_id']);
    $menu_order = intval($_POST['menu_order']);

    // 验证文章存在
    if (!get_post($post_id)) {
        wp_send_json_error(array('message' => __('栏目不存在', 'document-management')));
    }

    // 验证排序值
    if ($menu_order < 0 || $menu_order > 999) {
        wp_send_json_error(array('message' => __('排序值必须在0-999之间', 'document-management')));
    }

    // 更新排序
    $result = wp_update_post(array(
        'ID' => $post_id,
        'menu_order' => $menu_order
    ));

    if ($result) {
        // 清理缓存
        wp_cache_delete('column_manager_posts_' . md5(serialize(array())), 'document_management');

        wp_send_json_success(array('message' => __('排序更新成功', 'document-management')));
    } else {
        wp_send_json_error(array('message' => __('排序更新失败', 'document-management')));
    }
}

/**
 * 处理创建子栏目
 */
function ajax_create_sub_column() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'column_manager_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }

    // 检查用户权限
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }

    $parent_id = intval($_POST['parent_id']);
    $title = sanitize_text_field($_POST['title']);

    if (empty($title)) {
        wp_send_json_error(array('message' => __('标题不能为空', 'document-management')));
    }

    // 验证父文章存在
    $parent_post = get_post($parent_id);
    if (!$parent_post) {
        wp_send_json_error(array('message' => __('父栏目不存在', 'document-management')));
    }

    // 检查父文章是否已经是子级
    if ($parent_post->post_parent > 0) {
        wp_send_json_error(array('message' => __('最多只支持2级层级', 'document-management')));
    }

    // 创建子栏目
    $post_data = array(
        'post_title' => $title,
        'post_content' => '',
        'post_status' => 'draft',
        'post_type' => 'post',
        'post_parent' => $parent_id,
        'menu_order' => 0
    );

    $post_id = wp_insert_post($post_data);

    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => __('创建失败: ', 'document-management') . $post_id->get_error_message()));
    }

    if ($post_id) {
        // 设置深度元数据
        update_post_meta($post_id, '_column_depth', 1);

        // 清理缓存
        wp_cache_delete('column_manager_posts_' . md5(serialize(array())), 'document_management');

        wp_send_json_success(array(
            'message' => __('子栏目创建成功', 'document-management'),
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?action=edit&post=' . $post_id)
        ));
    } else {
        wp_send_json_error(array('message' => __('创建失败', 'document-management')));
    }
}

/**
 * 获取前端文档菜单
 */
function ajax_get_docs_menu() {
    // 验证nonce（对于公开访问，可以选择性验证）
    if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'docs_frontend_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }

    // 获取栏目管理器实例
    $column_manager = Column_Manager::get_instance();
    $hierarchical_posts = $column_manager->get_hierarchical_posts('publish');

    // 生成菜单HTML
    ob_start();
    render_docs_menu($hierarchical_posts);
    $html = ob_get_clean();

    wp_send_json_success(array(
        'html' => $html,
        'message' => __('菜单加载成功', 'document-management')
    ));
}

/**
 * 获取文档内容
 */
function ajax_get_doc_content() {
    // 验证nonce（对于公开访问，可以选择性验证）
    if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'docs_frontend_nonce')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }

    $post_id = intval($_POST['post_id']);

    // 获取文章
    $post = get_post($post_id);

    if (!$post || $post->post_status !== 'publish') {
        wp_send_json_error(array('message' => __('文档不存在或未发布', 'document-management')));
    }

    // 处理文章内容
    $content = apply_filters('the_content', $post->post_content);

    wp_send_json_success(array(
        'title' => $post->post_title,
        'content' => '<h1>' . esc_html($post->post_title) . '</h1>' . $content,
        'message' => __('文档加载成功', 'document-management')
    ));
}

/**
 * 渲染前端文档菜单
 */
function render_docs_menu($posts, $depth = 0) {
    if (empty($posts)) {
        if ($depth === 0) {
            echo '<div class="no-docs">暂无已发布的文档</div>';
        }
        return;
    }

    $list_class = $depth === 0 ? 'docs-menu' : 'docs-submenu';
    echo '<ul class="' . $list_class . '">';

    foreach ($posts as $post) {
        render_docs_menu_item($post, $depth);
    }

    echo '</ul>';
}

/**
 * 渲染单个菜单项
 */
function render_docs_menu_item($post, $depth = 0) {
    $slug = $post->post_name;
    $settings = get_option('document_management_settings', array('url_slug' => 'docs'));
    $url_slug = $settings['url_slug'];
    ?>
    <li class="docs-menu-item">
        <a href="/<?php echo $url_slug; ?>/<?php echo $slug; ?>"
           class="docs-menu-link"
           data-post-id="<?php echo $post->ID; ?>"
           data-slug="<?php echo $slug; ?>">
            <?php echo esc_html($post->post_title); ?>
        </a>

        <?php if (!empty($post->children)): ?>
            <?php render_docs_menu($post->children, $depth + 1); ?>
        <?php endif; ?>
    </li>
    <?php
}

/**
 * 刷新重写规则
 */
function ajax_flush_rewrite_rules() {
    // 验证nonce
    if (!wp_verify_nonce($_POST['nonce'], 'flush_rewrite_rules')) {
        wp_send_json_error(array('message' => __('安全验证失败', 'document-management')));
    }

    // 检查用户权限
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => __('权限不足', 'document-management')));
    }

    // 刷新重写规则
    flush_rewrite_rules();

    wp_send_json_success(array('message' => __('重写规则已刷新', 'document-management')));
}

/**
 * 检查是否为祖先文章
 */
function is_post_ancestor($ancestor_id, $post_id) {
    $post = get_post($post_id);

    while ($post && $post->post_parent) {
        if ($post->post_parent == $ancestor_id) {
            return true;
        }
        $post = get_post($post->post_parent);
    }

    return false;
}
