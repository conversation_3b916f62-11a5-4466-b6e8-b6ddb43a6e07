<?php
/**
 * 栏目管理核心类
 *
 * @package Document_Management
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 栏目管理器类
 */
class Column_Manager {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * 初始化
     */
    private function init() {
        // 添加管理菜单
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // 加载管理页面资源
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_menu_page(
            __('文档菜单管理', 'document-management'),
            __('文档菜单管理', 'document-management'),
            'edit_posts',
            'column-management',
            array($this, 'render_management_page'),
            'dashicons-menu-alt3',
            30
        );
    }
    
    /**
     * 加载管理页面脚本和样式
     */
    public function enqueue_admin_scripts($hook) {
        // 只在栏目管理页面加载资源
        if ('toplevel_page_column-management' !== $hook) {
            return;
        }
        
        // 加载jQuery UI
        wp_enqueue_script('jquery-ui-sortable');
        
        // 加载自定义脚本
        wp_enqueue_script(
            'column-manager-js',
            DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/js/column-manager.js',
            array('jquery', 'jquery-ui-sortable'),
            DOCUMENT_MANAGEMENT_VERSION,
            true
        );
        
        // 加载自定义样式
        wp_enqueue_style(
            'column-manager-css',
            DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/css/column-manager.css',
            array(),
            DOCUMENT_MANAGEMENT_VERSION
        );
        
        // 传递数据给JavaScript
        wp_localize_script('column-manager-js', 'columnManager', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('column_manager_nonce'),
            'strings' => array(
                'saving' => __('保存中...', 'document-management'),
                'saved' => __('已保存', 'document-management'),
                'error' => __('保存失败', 'document-management'),
                'confirmDelete' => __('确定要删除这个栏目吗？', 'document-management'),
            )
        ));
    }
    
    /**
     * 渲染管理页面
     */
    public function render_management_page() {
        // 检查用户权限
        if (!current_user_can('edit_posts')) {
            wp_die(__('您没有权限访问此页面。', 'document-management'));
        }
        
        // 获取层级文章数据
        $hierarchical_posts = $this->get_hierarchical_posts();

        // 调试信息
        $total_posts = 0;
        $total_children = 0;
        foreach ($hierarchical_posts as $post) {
            $total_posts++;
            $total_children += count($post->children);
        }

        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e('文档菜单管理', 'document-management'); ?></h1>
            <a href="<?php echo admin_url('post-new.php'); ?>" class="page-title-action">
                <?php _e('添加新文档', 'document-management'); ?>
            </a>
            <hr class="wp-header-end">


            
            <div id="column-manager-container">
                <div class="column-manager-toolbar">
                    <div class="alignleft actions">
                        <input type="text" id="search-posts" placeholder="<?php _e('搜索文档...', 'document-management'); ?>" class="search-input">
                        <select id="post-status-filter">
                            <option value=""><?php _e('所有状态', 'document-management'); ?></option>
                            <option value="publish"><?php _e('已发布', 'document-management'); ?></option>
                            <option value="draft"><?php _e('草稿', 'document-management'); ?></option>
                            <option value="private"><?php _e('私密', 'document-management'); ?></option>
                        </select>
                        <button type="button" class="button" id="filter-posts">
                            <?php _e('筛选', 'document-management'); ?>
                        </button>
                        <button type="button" class="button" id="clear-filters">
                            <?php _e('清除', 'document-management'); ?>
                        </button>
                    </div>
                    <div class="alignleft actions bulk-actions" style="margin-left: 20px;">
                        <input type="checkbox" id="select-all-posts" title="<?php _e('全选', 'document-management'); ?>">
                        <label for="select-all-posts" style="margin-right: 10px;"><?php _e('全选', 'document-management'); ?></label>
                        <select id="bulk-action">
                            <option value=""><?php _e('批量操作', 'document-management'); ?></option>
                            <option value="publish"><?php _e('设为已发布', 'document-management'); ?></option>
                            <option value="draft"><?php _e('设为草稿', 'document-management'); ?></option>
                            <option value="delete"><?php _e('删除', 'document-management'); ?></option>
                        </select>
                        <button type="button" class="button" id="apply-bulk-action">
                            <?php _e('应用', 'document-management'); ?>
                        </button>
                    </div>
                    <div class="alignright">
                        <span class="spinner"></span>
                        <span id="save-status"></span>
                    </div>
                    <br class="clear">
                </div>
                
                <div id="column-list-container">
                    <?php $this->render_column_list($hierarchical_posts); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 获取层级文章数据
     */
    public function get_hierarchical_posts($status = '', $search = '') {
        // 构建查询参数
        $args = array(
            'post_type' => 'post',
            'post_status' => $status ? $status : array('publish', 'draft', 'private', 'pending'),
            'posts_per_page' => -1,
            'orderby' => array('menu_order' => 'ASC', 'date' => 'DESC'),
            'order' => 'ASC',
            'suppress_filters' => false
        );

        // 添加搜索条件
        if (!empty($search)) {
            $args['s'] = $search;
        }

        // 添加缓存键
        $cache_key = 'column_manager_posts_' . md5(serialize($args));
        $posts = wp_cache_get($cache_key, 'document_management');

        if (false === $posts) {
            $posts = get_posts($args);
            // 缓存1小时
            wp_cache_set($cache_key, $posts, 'document_management', HOUR_IN_SECONDS);
        }

        // 构建层级结构
        return $this->build_hierarchy($posts);
    }
    
    /**
     * 构建层级结构
     */
    private function build_hierarchy($posts) {
        $hierarchy = array();
        $post_map = array();

        // 创建文章映射并初始化children数组
        foreach ($posts as $post) {
            $post_map[$post->ID] = $post;
            $post->children = array();
        }

        // 构建层级关系
        foreach ($posts as $post) {
            if ($post->post_parent == 0) {
                // 顶级文章
                $hierarchy[] = $post;
            } else {
                // 子文章
                if (isset($post_map[$post->post_parent])) {
                    $post_map[$post->post_parent]->children[] = $post;
                } else {
                    // 如果父文章不存在，将其作为顶级文章处理
                    $hierarchy[] = $post;
                }
            }
        }

        // 按menu_order排序
        usort($hierarchy, function($a, $b) {
            return $a->menu_order - $b->menu_order;
        });

        // 递归排序子项
        $this->sort_children($hierarchy);

        return $hierarchy;
    }

    /**
     * 递归排序子项
     */
    private function sort_children($posts) {
        foreach ($posts as $post) {
            if (!empty($post->children)) {
                usort($post->children, function($a, $b) {
                    return $a->menu_order - $b->menu_order;
                });
                $this->sort_children($post->children);
            }
        }
    }
    
    /**
     * 渲染栏目列表
     */
    public function render_column_list($posts, $depth = 0) {
        if (empty($posts)) {
            if ($depth === 0) {
                echo '<div class="no-items">';
                echo '<div class="no-items-icon">📄</div>';
                echo '<h3>' . __('暂无文档', 'document-management') . '</h3>';
                echo '<p>' . __('还没有创建任何文档。点击上方的"添加新文档"按钮开始创建您的第一个文档。', 'document-management') . '</p>';
                echo '<a href="' . admin_url('post-new.php') . '" class="button button-primary">' . __('创建第一个文档', 'document-management') . '</a>';
                echo '</div>';
            }
            return;
        }

        $list_class = $depth === 0 ? 'column-list sortable' : 'column-sublist sortable';
        echo '<ul class="' . $list_class . '" data-depth="' . $depth . '">';

        foreach ($posts as $post) {
            $this->render_column_item($post, $depth);
        }

        echo '</ul>';
    }
    
    /**
     * 渲染单个栏目项
     */
    public function render_column_item($post, $depth = 0) {
        $edit_url = admin_url('post.php?action=edit&post=' . $post->ID);
        $status_class = 'status-' . $post->post_status;
        $depth_class = 'depth-' . $depth;



        ?>
        <li class="column-item <?php echo $status_class . ' ' . $depth_class; ?>"
            data-id="<?php echo $post->ID; ?>"
            data-parent="<?php echo $post->post_parent; ?>"
            data-depth="<?php echo $depth; ?>">

            <div class="column-item-handle">
                <div class="column-item-checkbox">
                    <input type="checkbox" class="post-checkbox" value="<?php echo $post->ID; ?>" data-title="<?php echo esc_attr($post->post_title); ?>">
                </div>
                <div class="column-item-info">
                    <span class="column-item-title">
                        <strong><?php echo esc_html($post->post_title); ?></strong>
                        <span class="post-status">(<?php echo $this->get_status_label($post->post_status); ?>)</span>
                        <span class="order-display">排序: <?php echo $post->menu_order; ?></span>
                        <?php if (!empty($post->children)): ?>
                            <span class="children-count">[<?php echo count($post->children); ?> 子文档]</span>
                        <?php endif; ?>
                    </span>
                </div>

                <div class="column-item-controls">
                    <button type="button" class="button button-small edit-order-btn"
                            data-post-id="<?php echo $post->ID; ?>"
                            data-current-order="<?php echo $post->menu_order; ?>"
                            data-post-title="<?php echo esc_attr($post->post_title); ?>">
                        <?php _e('修改排序', 'document-management'); ?>
                    </button>
                    <a href="<?php echo $edit_url; ?>" class="button button-small">
                        <?php _e('编辑', 'document-management'); ?>
                    </a>
                    <?php if ($depth === 0): ?>
                        <button type="button" class="button button-small add-sub-column" data-parent-id="<?php echo $post->ID; ?>">
                            <?php _e('添加子文档', 'document-management'); ?>
                        </button>
                    <?php endif; ?>
                    <button type="button" class="button button-small delete-column" data-id="<?php echo $post->ID; ?>">
                        <?php _e('删除', 'document-management'); ?>
                    </button>
                </div>
            </div>

            <?php if (!empty($post->children)): ?>
                <?php $this->render_column_list($post->children, $depth + 1); ?>
            <?php endif; ?>
        </li>
        <?php
    }
    
    /**
     * 获取状态标签
     */
    private function get_status_label($status) {
        $labels = array(
            'publish' => __('已发布', 'document-management'),
            'draft' => __('草稿', 'document-management'),
            'private' => __('私密', 'document-management'),
            'pending' => __('待审核', 'document-management'),
        );

        return isset($labels[$status]) ? $labels[$status] : $status;
    }

    /**
     * 清理缓存
     */
    public function clear_cache() {
        wp_cache_flush_group('document_management');
    }

    /**
     * 获取插件版本信息
     */
    public function get_plugin_info() {
        return array(
            'version' => DOCUMENT_MANAGEMENT_VERSION,
            'name' => __('文档管理系统', 'document-management'),
            'description' => __('强大的文档管理系统，支持栏目层级管理和拖拽排序。', 'document-management')
        );
    }
}
