<?php
/**
 * 栏目管理核心类
 *
 * @package Document_Management
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 栏目管理器类
 */
class Column_Manager {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * 初始化
     */
    private function init() {
        // 添加管理菜单
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // 加载管理页面资源
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        // 主菜单
        add_menu_page(
            __('文档管理', 'document-management'),
            __('文档管理', 'document-management'),
            'edit_posts',
            'document-management',
            array($this, 'render_management_page'),
            'dashicons-menu-alt3',
            30
        );

        // 子菜单 - 文档菜单管理
        add_submenu_page(
            'document-management',
            __('文档菜单管理', 'document-management'),
            __('文档菜单管理', 'document-management'),
            'edit_posts',
            'document-management',
            array($this, 'render_management_page')
        );

        // 子菜单 - 设置
        add_submenu_page(
            'document-management',
            __('设置', 'document-management'),
            __('设置', 'document-management'),
            'manage_options',
            'document-management-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * 加载管理页面脚本和样式
     */
    public function enqueue_admin_scripts($hook) {
        // 只在文档管理相关页面加载资源
        if (!in_array($hook, array('toplevel_page_document-management', 'document-management_page_document-management-settings'))) {
            return;
        }

        // 设置页面不需要加载栏目管理的脚本
        if ($hook === 'document-management_page_document-management-settings') {
            return;
        }
        
        // 加载jQuery UI
        wp_enqueue_script('jquery-ui-sortable');
        
        // 加载自定义脚本
        wp_enqueue_script(
            'column-manager-js',
            DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/js/column-manager.js',
            array('jquery', 'jquery-ui-sortable'),
            DOCUMENT_MANAGEMENT_VERSION,
            true
        );
        
        // 加载自定义样式
        wp_enqueue_style(
            'column-manager-css',
            DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/css/column-manager.css',
            array(),
            DOCUMENT_MANAGEMENT_VERSION
        );
        
        // 传递数据给JavaScript
        wp_localize_script('column-manager-js', 'columnManager', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('column_manager_nonce'),
            'strings' => array(
                'saving' => __('保存中...', 'document-management'),
                'saved' => __('已保存', 'document-management'),
                'error' => __('保存失败', 'document-management'),
                'confirmDelete' => __('确定要删除这个栏目吗？', 'document-management'),
            )
        ));
    }
    
    /**
     * 渲染管理页面
     */
    public function render_management_page() {
        // 检查用户权限
        if (!current_user_can('edit_posts')) {
            wp_die(__('您没有权限访问此页面。', 'document-management'));
        }
        
        // 获取层级文章数据
        $hierarchical_posts = $this->get_hierarchical_posts();

        // 调试信息
        $total_posts = 0;
        $total_children = 0;
        foreach ($hierarchical_posts as $post) {
            $total_posts++;
            $total_children += count($post->children);
        }

        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e('文档菜单管理', 'document-management'); ?></h1>
            <a href="<?php echo admin_url('post-new.php'); ?>" class="page-title-action">
                <?php _e('添加新文档', 'document-management'); ?>
            </a>
            <hr class="wp-header-end">


            
            <div id="column-manager-container">
                <div class="column-manager-toolbar">
                    <div class="alignleft actions">
                        <select id="post-status-filter">
                            <option value=""><?php _e('所有状态', 'document-management'); ?></option>
                            <option value="publish"><?php _e('已发布', 'document-management'); ?></option>
                            <option value="draft"><?php _e('草稿', 'document-management'); ?></option>
                            <option value="private"><?php _e('私密', 'document-management'); ?></option>
                        </select>
                        <button type="button" class="button" id="filter-posts">
                            <?php _e('筛选', 'document-management'); ?>
                        </button>
                    </div>
                    <div class="alignright">
                        <span class="spinner"></span>
                        <span id="save-status"></span>
                    </div>
                    <br class="clear">
                </div>
                
                <div id="column-list-container">
                    <?php $this->render_column_list($hierarchical_posts); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 获取层级文章数据
     */
    public function get_hierarchical_posts($status = '') {
        // 构建查询参数
        $args = array(
            'post_type' => 'post',
            'post_status' => $status ? $status : array('publish', 'draft', 'private', 'pending'),
            'posts_per_page' => -1,
            'orderby' => array('menu_order' => 'ASC', 'date' => 'DESC'),
            'order' => 'ASC',
            'suppress_filters' => false
        );

        // 添加缓存键
        $cache_key = 'column_manager_posts_' . md5(serialize($args));
        $posts = wp_cache_get($cache_key, 'document_management');

        if (false === $posts) {
            $posts = get_posts($args);
            // 缓存1小时
            wp_cache_set($cache_key, $posts, 'document_management', HOUR_IN_SECONDS);
        }

        // 构建层级结构
        return $this->build_hierarchy($posts);
    }
    
    /**
     * 构建层级结构
     */
    private function build_hierarchy($posts) {
        $hierarchy = array();
        $post_map = array();

        // 创建文章映射并初始化children数组
        foreach ($posts as $post) {
            $post_map[$post->ID] = $post;
            $post->children = array();
        }

        // 构建层级关系
        foreach ($posts as $post) {
            if ($post->post_parent == 0) {
                // 顶级文章
                $hierarchy[] = $post;
            } else {
                // 子文章
                if (isset($post_map[$post->post_parent])) {
                    $post_map[$post->post_parent]->children[] = $post;
                } else {
                    // 如果父文章不存在，将其作为顶级文章处理
                    $hierarchy[] = $post;
                }
            }
        }

        // 按menu_order排序
        usort($hierarchy, function($a, $b) {
            return $a->menu_order - $b->menu_order;
        });

        // 递归排序子项
        $this->sort_children($hierarchy);

        return $hierarchy;
    }

    /**
     * 递归排序子项
     */
    private function sort_children($posts) {
        foreach ($posts as $post) {
            if (!empty($post->children)) {
                usort($post->children, function($a, $b) {
                    return $a->menu_order - $b->menu_order;
                });
                $this->sort_children($post->children);
            }
        }
    }
    
    /**
     * 渲染栏目列表
     */
    public function render_column_list($posts, $depth = 0) {
        if (empty($posts)) {
            if ($depth === 0) {
                echo '<div class="no-items">';
                echo '<div class="no-items-icon">📄</div>';
                echo '<h3>' . __('暂无文档', 'document-management') . '</h3>';
                echo '<p>' . __('还没有创建任何文档。点击上方的"添加新文档"按钮开始创建您的第一个文档。', 'document-management') . '</p>';
                echo '<a href="' . admin_url('post-new.php') . '" class="button button-primary">' . __('创建第一个文档', 'document-management') . '</a>';
                echo '</div>';
            }
            return;
        }

        $list_class = $depth === 0 ? 'column-list sortable' : 'column-sublist sortable';
        echo '<ul class="' . $list_class . '" data-depth="' . $depth . '">';

        foreach ($posts as $post) {
            $this->render_column_item($post, $depth);
        }

        echo '</ul>';
    }
    
    /**
     * 渲染单个栏目项
     */
    public function render_column_item($post, $depth = 0) {
        $edit_url = admin_url('post.php?action=edit&post=' . $post->ID);
        $status_class = 'status-' . $post->post_status;
        $depth_class = 'depth-' . $depth;



        ?>
        <li class="column-item <?php echo $status_class . ' ' . $depth_class; ?>"
            data-id="<?php echo $post->ID; ?>"
            data-parent="<?php echo $post->post_parent; ?>"
            data-depth="<?php echo $depth; ?>">

            <div class="column-item-handle">
                <div class="column-item-info">
                    <span class="column-item-title">
                        <strong><?php echo esc_html($post->post_title); ?></strong>
                        <span class="post-status">(<?php echo $this->get_status_label($post->post_status); ?>)</span>
                        <span class="order-display">排序: <?php echo $post->menu_order; ?></span>
                        <?php if (!empty($post->children)): ?>
                            <span class="children-count">[<?php echo count($post->children); ?> 子文档]</span>
                        <?php endif; ?>
                    </span>
                </div>

                <div class="column-item-controls">
                    <button type="button" class="button button-small edit-order-btn"
                            data-post-id="<?php echo $post->ID; ?>"
                            data-current-order="<?php echo $post->menu_order; ?>"
                            data-post-title="<?php echo esc_attr($post->post_title); ?>">
                        <?php _e('修改排序', 'document-management'); ?>
                    </button>
                    <a href="<?php echo $edit_url; ?>" class="button button-small">
                        <?php _e('编辑', 'document-management'); ?>
                    </a>
                    <?php if ($depth === 0): ?>
                        <button type="button" class="button button-small add-sub-column" data-parent-id="<?php echo $post->ID; ?>">
                            <?php _e('添加子文档', 'document-management'); ?>
                        </button>
                    <?php endif; ?>
                    <button type="button" class="button button-small delete-column" data-id="<?php echo $post->ID; ?>">
                        <?php _e('删除', 'document-management'); ?>
                    </button>
                </div>
            </div>

            <?php if (!empty($post->children)): ?>
                <?php $this->render_column_list($post->children, $depth + 1); ?>
            <?php endif; ?>
        </li>
        <?php
    }
    
    /**
     * 获取状态标签
     */
    private function get_status_label($status) {
        $labels = array(
            'publish' => __('已发布', 'document-management'),
            'draft' => __('草稿', 'document-management'),
            'private' => __('私密', 'document-management'),
            'pending' => __('待审核', 'document-management'),
        );

        return isset($labels[$status]) ? $labels[$status] : $status;
    }

    /**
     * 清理缓存
     */
    public function clear_cache() {
        wp_cache_flush_group('document_management');
    }

    /**
     * 渲染设置页面
     */
    public function render_settings_page() {
        // 检查用户权限
        if (!current_user_can('manage_options')) {
            wp_die(__('您没有权限访问此页面。', 'document-management'));
        }

        // 处理表单提交
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'document_management_settings')) {
            $this->save_settings();
        }

        // 获取当前设置
        $settings = $this->get_settings();

        ?>
        <div class="wrap">
            <h1><?php _e('文档管理设置', 'document-management'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('document_management_settings'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="docs_url_slug"><?php _e('文档URL路径', 'document-management'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="docs_url_slug" name="docs_url_slug"
                                   value="<?php echo esc_attr($settings['url_slug']); ?>"
                                   class="regular-text" placeholder="docs">
                            <p class="description">
                                <?php _e('设置文档页面的URL路径，例如设置为 "docs" 则访问地址为 /docs', 'document-management'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(__('保存设置', 'document-management')); ?>
            </form>

            <hr>

            <h2><?php _e('重写规则检查', 'document-management'); ?></h2>
            <div class="rewrite-rules-check">
                <?php $this->render_rewrite_rules_check($settings); ?>
            </div>

            <hr>

            <h2><?php _e('系统信息', 'document-management'); ?></h2>
            <div class="system-info">
                <?php $this->render_system_info(); ?>
            </div>
        </div>
        <?php
    }

    /**
     * 获取设置
     */
    private function get_settings() {
        $defaults = array(
            'url_slug' => 'docs'
        );

        $settings = get_option('document_management_settings', $defaults);
        return wp_parse_args($settings, $defaults);
    }

    /**
     * 保存设置
     */
    private function save_settings() {
        $settings = array(
            'url_slug' => sanitize_text_field($_POST['docs_url_slug'])
        );

        // 验证URL slug
        if (empty($settings['url_slug'])) {
            $settings['url_slug'] = 'docs';
        }

        // 保存设置
        update_option('document_management_settings', $settings);

        // 刷新重写规则
        flush_rewrite_rules();

        // 显示成功消息
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('设置已保存并刷新了重写规则。', 'document-management') . '</p></div>';
        });
    }

    /**
     * 渲染重写规则检查
     */
    private function render_rewrite_rules_check($settings) {
        $url_slug = $settings['url_slug'];
        $test_url = home_url('/' . $url_slug . '/');

        ?>
        <div class="rewrite-check-section">
            <h3><?php _e('当前设置', 'document-management'); ?></h3>
            <p><strong><?php _e('文档URL:', 'document-management'); ?></strong>
               <a href="<?php echo $test_url; ?>" target="_blank"><?php echo $test_url; ?></a>
            </p>

            <h3><?php _e('重写规则状态', 'document-management'); ?></h3>
            <div id="rewrite-status">
                <button type="button" class="button" onclick="checkRewriteRules()">
                    <?php _e('检查重写规则', 'document-management'); ?>
                </button>
                <div id="rewrite-result" style="margin-top: 10px;"></div>
            </div>

            <h3><?php _e('手动刷新', 'document-management'); ?></h3>
            <p>
                <button type="button" class="button" onclick="flushRewriteRules()">
                    <?php _e('刷新重写规则', 'document-management'); ?>
                </button>
                <span class="description"><?php _e('如果文档页面无法访问，请点击此按钮刷新重写规则。', 'document-management'); ?></span>
            </p>
        </div>

        <script>
        function checkRewriteRules() {
            var resultDiv = document.getElementById('rewrite-result');
            resultDiv.innerHTML = '<span class="spinner is-active"></span> 检查中...';

            fetch('<?php echo $test_url; ?>')
                .then(response => {
                    if (response.ok) {
                        resultDiv.innerHTML = '<span style="color: green;">✓ 重写规则工作正常</span>';
                    } else {
                        resultDiv.innerHTML = '<span style="color: red;">✗ 重写规则可能有问题 (状态码: ' + response.status + ')</span>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<span style="color: red;">✗ 检查失败: ' + error.message + '</span>';
                });
        }

        function flushRewriteRules() {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', ajaxurl, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        alert('重写规则已刷新！');
                    } else {
                        alert('刷新失败，请手动刷新页面。');
                    }
                }
            };
            xhr.send('action=flush_rewrite_rules&nonce=<?php echo wp_create_nonce("flush_rewrite_rules"); ?>');
        }
        </script>
        <?php
    }

    /**
     * 渲染系统信息
     */
    private function render_system_info() {
        $plugin_info = $this->get_plugin_info();

        ?>
        <table class="widefat">
            <tbody>
                <tr>
                    <td><strong><?php _e('插件版本', 'document-management'); ?></strong></td>
                    <td><?php echo $plugin_info['version']; ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('WordPress版本', 'document-management'); ?></strong></td>
                    <td><?php echo get_bloginfo('version'); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('PHP版本', 'document-management'); ?></strong></td>
                    <td><?php echo PHP_VERSION; ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('当前主题', 'document-management'); ?></strong></td>
                    <td><?php echo wp_get_theme()->get('Name'); ?></td>
                </tr>
                <tr>
                    <td><strong><?php _e('永久链接结构', 'document-management'); ?></strong></td>
                    <td><?php echo get_option('permalink_structure') ?: '默认'; ?></td>
                </tr>
            </tbody>
        </table>
        <?php
    }

    /**
     * 获取插件版本信息
     */
    public function get_plugin_info() {
        return array(
            'version' => DOCUMENT_MANAGEMENT_VERSION,
            'name' => __('文档管理系统', 'document-management'),
            'description' => __('强大的文档管理系统，支持栏目层级管理和拖拽排序。', 'document-management')
        );
    }
}
