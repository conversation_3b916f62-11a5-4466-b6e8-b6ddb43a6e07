# 文档管理系统插件

一个强大的WordPress文档管理系统插件，提供栏目管理功能，支持多级菜单的拖拽排序。

## 功能特性

- ✅ **栏目管理** - 在WordPress后台添加专门的栏目管理菜单
- ✅ **层级结构** - 支持最多2级的栏目层级管理
- ✅ **拖拽排序** - 直观的拖拽界面，实时排序调整
- ✅ **无缝集成** - 编辑功能直接跳转到WordPress原生文章编辑页面
- ✅ **状态筛选** - 支持按发布状态筛选栏目
- ✅ **响应式设计** - 适配各种设备屏幕
- ✅ **安全可靠** - 完整的权限检查和数据验证

## 安装方法

1. 将插件文件夹上传到 `wp-content/plugins/` 目录
2. 在WordPress后台的"插件"页面激活插件
3. 在左侧菜单中找到"栏目管理"开始使用

## 使用说明

### 访问栏目管理
激活插件后，在WordPress后台左侧菜单中会出现"栏目管理"选项，点击即可进入管理页面。

### 创建栏目
1. 点击页面顶部的"添加新栏目"按钮
2. 在WordPress标准的文章编辑页面中创建内容
3. 发布后返回栏目管理页面即可看到新栏目

### 拖拽排序
- 鼠标悬停在栏目项上，光标会变为移动图标
- 拖拽栏目项到目标位置即可调整排序
- 支持在不同层级之间移动（最多2级）
- 排序会自动保存

### 层级管理
- 将栏目拖拽到另一个栏目下方可创建子栏目
- 最多支持2级层级（父栏目 → 子栏目）
- 子栏目会有视觉缩进显示

### 状态筛选
使用页面顶部的状态筛选器可以按以下状态筛选栏目：
- 所有状态
- 已发布
- 草稿
- 私密

### 编辑和删除
- 点击"编辑"按钮跳转到WordPress原生编辑页面
- 点击"删除"按钮可删除栏目（需要先删除子栏目）

## 技术要求

- WordPress 5.0 或更高版本
- PHP 7.4 或更高版本
- 支持JavaScript的现代浏览器

## 权限要求

使用栏目管理功能需要以下WordPress权限：
- `edit_posts` - 查看和编辑栏目
- `delete_posts` - 删除栏目

## 开发信息

### 文件结构
```
document-management/
├── document-management.php     # 主插件文件
├── includes/
│   ├── class-column-manager.php    # 核心管理类
│   └── ajax-handlers.php           # AJAX处理函数
├── assets/
│   ├── js/
│   │   └── column-manager.js       # 前端交互脚本
│   └── css/
│       └── column-manager.css      # 样式文件
└── README.md                       # 说明文档
```

### 数据存储
插件利用WordPress现有的数据结构：
- 使用 `posts` 表存储栏目数据
- 使用 `post_parent` 字段建立层级关系
- 使用 `menu_order` 字段控制排序
- 使用 `_column_depth` 元数据标记层级深度

### 钩子和过滤器
插件提供以下钩子供开发者扩展：
- `document_management_before_render` - 渲染页面前
- `document_management_after_update` - 更新排序后

## 常见问题

**Q: 为什么拖拽后排序没有保存？**
A: 请检查是否有JavaScript错误，确保浏览器支持现代JavaScript特性。

**Q: 可以支持超过2级的层级吗？**
A: 当前版本限制最多2级层级，这是为了保持界面简洁和性能考虑。

**Q: 插件会影响现有文章吗？**
A: 不会，插件只是利用WordPress的文章系统，不会修改现有数据结构。

## 更新日志

### 1.0.0
- 初始版本发布
- 基础栏目管理功能
- 拖拽排序支持
- 2级层级管理
- 状态筛选功能

## 支持

如有问题或建议，请联系开发者或在项目页面提交Issue。

## 许可证

本插件基于GPL v2或更高版本许可证发布。
